{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["TN6ZkmOSTq6xSCeDASB6+54LSyssszhu/ECA6xZFfX8=", "OTNmf7A+fqVnJM2XDbncjC7ylhyLLS3v1uRsae3mf1k=", "kPX4OzlvSY2z1dJ2DcaKK6tZhx1JS2cPE0mUDdhuFWQ=", "6ybH+w3KBze1GVm7oHIQb34y12uZclJyAU9LiAYXG3E=", "BlKniL7TLTxy2sEwV1Sr6u/6e16rcAD3arhGSvE4sCs=", "O6aNfGdD3dYgnmoIEtEGKXCTgmVgZFVXKTF7XqgF9e8=", "K0KXjKNr4dY/apaTAoM2oot+3ESYyuva412mSxebcrg=", "i5Zp90TvEhRI2rUxAbQILyeyc46TMZSRkycCnM4UeAI=", "LWLNi3IQAA3IH29pWxaaP6QdbOdWLwQIlhEG0AtUZqw=", "2WY3hDVxQxqmmhrw27OlPGM+tJebtBjsuik22qQ7YD4=", "HXQThjFnogsjKmosJzCIpeEYDTX/4yO/hSF15HHfMFM=", "pvX1bOLxJsZL/VJGgNgGkksdYSjsQgou7gzYdvuu6Gc=", "R9dg4EvXu1/mCktNf6vUJ6RWS+gTfQhmCBiO4Zc5frs=", "n5GGrKpLKcDxYpmy3RwgFSKlztdf8Z5gfpyzTvov1jc=", "2KfvSEj12Zno7SLAqYNupT1R2pGTKuPBbEvC//YIO40=", "x85bOYvC9foazqwt1Bf79aBWd/ICNUrBwouC64zkYmY=", "AtANVsvtJ4/MpXPjzayrPRYzOM3dZRHg+Gbr4FwXs+4=", "hRpa+5hCcX1vOft9iCwBaBN3t3Nl7YdQ/tilhVi247o=", "Bc47gXFNiAI+L5pCRIIjDtEKAG62mFCVGb+8bSKePr8=", "4wUt9GAxDu5gZTUN/H7LNlbckOQFFBT1yJs6pTRCTXg=", "80VLgXHtF8drMZzTKsoK3oOJWm2SnDIjhvupIR6x4qE=", "WwwIontaCi89Y9+NT7bHU6GV+jHDs+d/h81xjPUFU/A=", "vOAGZcojy2DP6twph/KTvjp4BRkRqnGk0F0+uCkqdkU=", "RiUmuTqg3RT2FOJR/gYjMsYM0b0vLsjEeWy2fe46tpk=", "nLppZaI1RxQKKTdvxM30iSffByXC8fpclsu0OgUIKIE=", "o9ryDZrvy2osyYilZig9dgs4vrIDFXE0SfB4HzRfclo=", "JfzOOKKmmC2nxoySVzLJyj/LQkK2yQpQ2AWN/raHjTc=", "Rablm4GfFcp7QGTwSHei0MX3y9/5adDmenP+g5j1Evg=", "4zPpdikPr4eMoNgzq8WXq0BqAruj2S0rwfF/jWVaZJU=", "ESXjDhH1AKXyLSsoP48Yq3FoO20dpz4+z4PK+sS2IrE=", "H4TWTf1HiJxqUI4hNkcXU0wPqTqzheLAQFV5ezR8RNg=", "fRrixD2wY7wPdKB0GKOxPQChZiV9X38j0AbmZI2l9GA=", "Cy9I9lWi9KC6feZVRkKN3qf2LI4VwzBAODRiB9NCWvU=", "TxQj+247rRnk2YAXg/Jk/D0wrwqV7wFbs+XQD4pvdM4=", "YEN4Z/cFRTkLrVY6FiXFtUGt0JQRi3COnv8q2JcMpU4=", "DJ4KuaUrdm2rE6cj/8ouqUcAN8tbSLNL70s4bL1uSUU=", "yfEwvlVGjZRSalYFrksB6nzH/b5E3HHu/PMP58YWpMk=", "9eufixY3hLdb/SZWt5OTaojJv4MclT/j6gkoBjrSy6U=", "HAvv0TYz4Z/qGZ1pviSShnkLrGMJMfapju+7Zx4e6Y4=", "bRdoXE+FZfK0Y9UZNMlbzucTki1690ah/Wqne2qOwJU=", "chX4hikGUMTERYyOMhFYG1SLl8gUZaZfpfprHivTaYI=", "uJSMXIzsKBT+22G6pBTIIaqJ1Jm6wnC1/EBmqBWabzg=", "Xluq9Py1s2Vl31THJwli77t9euXP0COaG4IxOvu8EZA=", "03uTgWErjkV/SmIYMMreveBcr4EcmKg+K7rEj/VxDCI=", "iPvu6qN3WyMoGdo/961xBhtX8tZp4ZKgQk5P4+ZDn6I=", "nIBH+M1zkR0ld1XAO4sdgBpb4xDNln5uB5U2UtjmxiU=", "gHKuK5TCmfwwPZuuLEt4l3UE1Yh0EZIzJ5/ApUnf0R4="], "CachedAssets": {"TN6ZkmOSTq6xSCeDASB6+54LSyssszhu/ECA6xZFfX8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\8wj9a6xfm3-cukhpdzbwt.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "app#[.{fingerprint=cukhpdzbwt}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aiem689ymb", "Integrity": "8uhMeihdSkkzZcJ+YI5bhtnh6rrZ1QicsBGVtbLFxs8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\app.css", "FileLength": 1842, "LastWriteTime": "2025-09-05T15:14:49.1723831+00:00"}, "OTNmf7A+fqVnJM2XDbncjC7ylhyLLS3v1uRsae3mf1k=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\v3c4xiwh69-bqjiyaj88i.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-09-05T15:14:49.1589294+00:00"}, "kPX4OzlvSY2z1dJ2DcaKK6tZhx1JS2cPE0mUDdhuFWQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\nczkc0oh6d-c2jlpeoesf.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-09-05T15:14:49.1884966+00:00"}, "6ybH+w3KBze1GVm7oHIQb34y12uZclJyAU9LiAYXG3E=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\06uxj7zjkz-erw9l3u2r3.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-09-05T15:14:49.1595512+00:00"}, "BlKniL7TLTxy2sEwV1Sr6u/6e16rcAD3arhGSvE4sCs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\sxj2is38sp-aexeepp0ev.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-09-05T15:14:49.1703753+00:00"}, "O6aNfGdD3dYgnmoIEtEGKXCTgmVgZFVXKTF7XqgF9e8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\6c5ib5i73w-d7shbmvgxk.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-09-05T15:14:49.1755961+00:00"}, "K0KXjKNr4dY/apaTAoM2oot+3ESYyuva412mSxebcrg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\umzrtfh081-ausgxo2sd3.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-09-05T15:14:49.1905053+00:00"}, "i5Zp90TvEhRI2rUxAbQILyeyc46TMZSRkycCnM4UeAI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\7woyaxizjx-k8d9w2qqmf.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-09-05T15:14:49.1583069+00:00"}, "LWLNi3IQAA3IH29pWxaaP6QdbOdWLwQIlhEG0AtUZqw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\dst7ld8icy-cosvhxvwiu.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-09-05T15:14:49.2081927+00:00"}, "2WY3hDVxQxqmmhrw27OlPGM+tJebtBjsuik22qQ7YD4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\tqg806j35l-ub07r2b239.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-09-05T15:14:49.1723831+00:00"}, "HXQThjFnogsjKmosJzCIpeEYDTX/4yO/hSF15HHfMFM=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\1yjclpun1e-fvhpjtyr6v.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-09-05T15:14:49.1871749+00:00"}, "pvX1bOLxJsZL/VJGgNgGkksdYSjsQgou7gzYdvuu6Gc=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\6jy6smkm3i-b7pk76d08c.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-09-05T15:14:49.1776056+00:00"}, "R9dg4EvXu1/mCktNf6vUJ6RWS+gTfQhmCBiO4Zc5frs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\r4tmmlvt65-fsbi9cje9m.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-09-05T15:14:49.1703753+00:00"}, "n5GGrKpLKcDxYpmy3RwgFSKlztdf8Z5gfpyzTvov1jc=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\9dkysd7rle-rzd6atqjts.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-09-05T15:14:49.1723831+00:00"}, "2KfvSEj12Zno7SLAqYNupT1R2pGTKuPBbEvC//YIO40=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\zcd9zgznws-ee0r1s7dh0.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-09-05T15:14:49.1945284+00:00"}, "x85bOYvC9foazqwt1Bf79aBWd/ICNUrBwouC64zkYmY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\xziqsr3sz7-dxx9fxp4il.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-09-05T15:14:49.1723831+00:00"}, "AtANVsvtJ4/MpXPjzayrPRYzOM3dZRHg+Gbr4FwXs+4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\aswh83urff-jd9uben2k1.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-09-05T15:14:49.1776056+00:00"}, "hRpa+5hCcX1vOft9iCwBaBN3t3Nl7YdQ/tilhVi247o=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\pa9hjgfrnm-khv3u5hwcm.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-09-05T15:14:49.1851665+00:00"}, "Bc47gXFNiAI+L5pCRIIjDtEKAG62mFCVGb+8bSKePr8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\x3jdasiu8y-r4e9w2rdcm.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-09-05T15:14:49.1905053+00:00"}, "4wUt9GAxDu5gZTUN/H7LNlbckOQFFBT1yJs6pTRCTXg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\wext3iovx4-lcd1t2u6c8.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-09-05T15:14:49.1755961+00:00"}, "80VLgXHtF8drMZzTKsoK3oOJWm2SnDIjhvupIR6x4qE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\oe4trwglh2-c2oey78nd0.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-09-05T15:14:49.1811482+00:00"}, "WwwIontaCi89Y9+NT7bHU6GV+jHDs+d/h81xjPUFU/A=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\0u6fmszudv-tdbxkamptv.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-09-05T15:14:49.1851665+00:00"}, "vOAGZcojy2DP6twph/KTvjp4BRkRqnGk0F0+uCkqdkU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\mnsxrep1aa-j5mq2jizvt.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-09-05T15:14:49.1884966+00:00"}, "RiUmuTqg3RT2FOJR/gYjMsYM0b0vLsjEeWy2fe46tpk=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\k3famkzv18-06098lyss8.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-09-05T15:14:49.2061823+00:00"}, "nLppZaI1RxQKKTdvxM30iSffByXC8fpclsu0OgUIKIE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\ww1xf0hof5-nvvlpmu67g.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-09-05T15:14:49.1884966+00:00"}, "o9ryDZrvy2osyYilZig9dgs4vrIDFXE0SfB4HzRfclo=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\8ml77bvuwx-s35ty4nyc5.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-09-05T15:14:49.2021728+00:00"}, "JfzOOKKmmC2nxoySVzLJyj/LQkK2yQpQ2AWN/raHjTc=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\3s2d8bmf9r-pj5nd1wqec.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-09-05T15:14:49.2347047+00:00"}, "Rablm4GfFcp7QGTwSHei0MX3y9/5adDmenP+g5j1Evg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\bg9fs4yg00-46ein0sx1k.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-09-05T15:14:49.1968458+00:00"}, "4zPpdikPr4eMoNgzq8WXq0BqAruj2S0rwfF/jWVaZJU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\xlfo1h6hub-v0zj4ognzu.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-09-05T15:14:49.2188408+00:00"}, "ESXjDhH1AKXyLSsoP48Yq3FoO20dpz4+z4PK+sS2IrE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\1pvotustu6-37tfw0ft22.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-09-05T15:14:49.2381019+00:00"}, "H4TWTf1HiJxqUI4hNkcXU0wPqTqzheLAQFV5ezR8RNg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\c1jo4zcikl-hrwsygsryq.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-09-05T15:14:49.2208507+00:00"}, "fRrixD2wY7wPdKB0GKOxPQChZiV9X38j0AbmZI2l9GA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\swygz4qrrc-pk9g2wxc8p.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-09-05T15:14:49.1884966+00:00"}, "Cy9I9lWi9KC6feZVRkKN3qf2LI4VwzBAODRiB9NCWvU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\f33ulydyvf-ft3s53vfgj.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-09-05T15:14:49.2315304+00:00"}, "TxQj+247rRnk2YAXg/Jk/D0wrwqV7wFbs+XQD4pvdM4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\oswslqjctf-6cfz1n2cew.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-09-05T15:14:49.1981496+00:00"}, "YEN4Z/cFRTkLrVY6FiXFtUGt0JQRi3COnv8q2JcMpU4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\ncmmg44yhf-6pdc2jztkx.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-09-05T15:14:49.2219157+00:00"}, "DJ4KuaUrdm2rE6cj/8ouqUcAN8tbSLNL70s4bL1uSUU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\hli4tr7c9b-493y06b0oq.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-09-05T15:14:49.236092+00:00"}, "yfEwvlVGjZRSalYFrksB6nzH/b5E3HHu/PMP58YWpMk=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\wnft3hzql8-iovd86k7lj.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-09-05T15:14:49.2188408+00:00"}, "9eufixY3hLdb/SZWt5OTaojJv4MclT/j6gkoBjrSy6U=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\449wmg44jb-vr1egmr9el.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-09-05T15:14:49.1905053+00:00"}, "HAvv0TYz4Z/qGZ1pviSShnkLrGMJMfapju+7Zx4e6Y4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\3y47zky8gu-kbrnm935zg.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-09-05T15:14:49.2188408+00:00"}, "bRdoXE+FZfK0Y9UZNMlbzucTki1690ah/Wqne2qOwJU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\6m8zv3ujgl-jj8uyg4cgr.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-09-05T15:14:49.1871749+00:00"}, "chX4hikGUMTERYyOMhFYG1SLl8gUZaZfpfprHivTaYI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\kpvb06ghsv-y7v9cxd14o.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-09-05T15:14:49.1965359+00:00"}, "uJSMXIzsKBT+22G6pBTIIaqJ1Jm6wnC1/EBmqBWabzg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\qa87lsj558-notf2xhcfb.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-09-05T15:14:49.2102007+00:00"}, "Xluq9Py1s2Vl31THJwli77t9euXP0COaG4IxOvu8EZA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\xuiidw9alg-h1s4sie4z3.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-09-05T15:14:49.214821+00:00"}, "03uTgWErjkV/SmIYMMreveBcr4EcmKg+K7rEj/VxDCI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\fsb1tkfgg3-63fj8s7r0e.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-09-05T15:14:49.2021728+00:00"}, "iPvu6qN3WyMoGdo/961xBhtX8tZp4ZKgQk5P4+ZDn6I=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\lyi2ct07jp-0j3bgjxly4.gz", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-09-05T15:14:49.2188408+00:00"}, "nIBH+M1zkR0ld1XAO4sdgBpb4xDNln5uB5U2UtjmxiU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\tsmidcw56f-tlh3hwsi09.gz", "SourceId": "OneHousingApiTester", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "OneHousingApiTester#[.{fingerprint=tlh3hwsi09}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\scopedcss\\bundle\\OneHousingApiTester.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "33iqfnv2u8", "Integrity": "D3mlM9DPm7R9sG8wR5oV9A5D1AN5TxGJYML8zZpnP4U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\scopedcss\\bundle\\OneHousingApiTester.styles.css", "FileLength": 1786, "LastWriteTime": "2025-09-05T15:14:49.1776056+00:00"}, "gHKuK5TCmfwwPZuuLEt4l3UE1Yh0EZIzJ5/ApUnf0R4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\tj0oqo0bgr-tlh3hwsi09.gz", "SourceId": "OneHousingApiTester", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "OneHousingApiTester#[.{fingerprint=tlh3hwsi09}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\OneHousingApiTester.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "33iqfnv2u8", "Integrity": "D3mlM9DPm7R9sG8wR5oV9A5D1AN5TxGJYML8zZpnP4U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\OneHousingApiTester.bundle.scp.css", "FileLength": 1786, "LastWriteTime": "2025-09-05T15:14:49.2001625+00:00"}}, "CachedCopyCandidates": {}}