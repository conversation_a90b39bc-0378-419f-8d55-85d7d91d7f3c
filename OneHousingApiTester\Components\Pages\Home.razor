﻿@page "/"

<PageTitle>OneHousing API Tester</PageTitle>

<h1>OneHousing API Tester</h1>

<div class="alert alert-info">
    <h4>Welcome to the OneHousing API Testing Application</h4>
    <p>This application allows you to test OneHousing API endpoints with XML payloads.</p>
</div>

<div class="row">
    <div class="col-md-8">
        <h2>Features</h2>
        <ul>
            <li><strong>XML Request Builder:</strong> Configure API requests with custom parameters</li>
            <li><strong>Real-time Preview:</strong> See the XML payload before sending</li>
            <li><strong>Response Viewer:</strong> View formatted XML responses and raw data</li>
            <li><strong>Error Handling:</strong> Clear error messages and status codes</li>
        </ul>

        <h2>Getting Started</h2>
        <p>Click on the <strong>API Tester</strong> link in the navigation menu to start testing the OneHousing API endpoints.</p>

        <p>The default configuration is set up for the OneHousing test environment:</p>
        <ul>
            <li><strong>URL:</strong> https://portaltest.melinhomes.co.uk/oa.test/webservice.p</li>
            <li><strong>Request Type:</strong> GETPLACE</li>
            <li><strong>Sample Property Reference:</strong> 8763</li>
        </ul>

        <a href="/api-tester" class="btn btn-primary btn-lg">Start Testing API</a>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Sample XML Request</h5>
            </div>
            <div class="card-body">
                <pre class="small"><code>&lt;message&gt;
  &lt;Header&gt;
    &lt;Security username='SNAPONE'
              password='U33@8RchyyRA2Mg3' /&gt;
  &lt;/Header&gt;
  &lt;Body&gt;
    &lt;Request request_type='GETPLACE'&gt;
      &lt;Parameters attribute='place_ref'
                  attribute_value='8763' /&gt;
    &lt;/Request&gt;
  &lt;/Body&gt;
&lt;/message&gt;</code></pre>
            </div>
        </div>
    </div>
</div>
