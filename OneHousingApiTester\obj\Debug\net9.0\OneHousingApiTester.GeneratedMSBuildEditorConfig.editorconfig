is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = OneHousingApiTester
build_property.RootNamespace = OneHousingApiTester
build_property.ProjectDir = C:\Users\<USER>\OneDrive - Hedyn\Documents\augment-projects\Onehousing API tester\OneHousingApiTester\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive - Hedyn\Documents\augment-projects\Onehousing API tester\OneHousingApiTester
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Pages/ApiTester.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBcGlUZXN0ZXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-50eorq6ywm

[C:/Users/<USER>/OneDrive - Hedyn/Documents/augment-projects/Onehousing API tester/OneHousingApiTester/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-d5q3oy8o6v
