{"GlobalPropertiesHash": "ocFZC4b7MRJalpE6UXtnKoZK5xGANCq0zk3qKxqkvgo=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["zmfMg+L9oV5b2ctSFTNbRaMRyfeHkyj6goXtNZvzSTw=", "S1SOZBtAt3QTFbZ6I86s1QKp/ToZgeltVIMlA+LY1kw=", "47A14t5es/JUSNotjqEJOYZ+bTxukI9QWYhnZIyXiDE=", "S8KXfOG8Md976OAWa+J+sSvypyvox1z6X9A61x8djKE=", "FpJLMwtwjmeC9nw4AyH7Bflqe3UrzE8fBR1uM6/HQoE=", "eFFyuKujv752qMxWAWgP5FuCsxYV4pB7tNtLuIbMJIs=", "eYTfDswpJ8NRd+Kcb0Xf5j0y7UH1GlDiz5ySSyS4ghQ=", "4vFvrSZdtLXAeeiwwJ2BU+QbCIHf0aGZA8gxZ2zA8o4=", "AlIjdrut1iw/gbNzWHTNZktLNwPkP/KMUj3QmvB5goA=", "sbqMFFJJvIZWN2co1UoSYTHaf9xW5e7YxmZV6FPloD4=", "m4xKa0KWDV4cOvEPc4Y+sbj6NbJeOEi3M5urduHXNO4=", "fdkyQWn9q4CcaH+66AYdf08F5AyJxfDUlIhqScgO7uQ=", "x39EPruyB2IcqOk5rcgJaVNlSuu+PJ/kDeBi7aj2tgw=", "wJiA12qaAJZhXHubZ6Y11u5a96p5WMse9N6MvP7IuUI=", "o2euMkQnr6q09bCN1IknMtWZWk8c8bRu+TZQA6ot5e4=", "HyWjMXy70mMjYORF6hKtWISf2qzxbmtTB1xlRV2qmPs=", "VtYmQHBaivCnZTXnMMvWpokFqRqfJqAzV8NrTNQ+duY=", "l4YSFEqCuQ5s4pzUDweMFLc1JRb+Gd4kvcFWUqimT+E=", "AkX7Jq13/q1jK55nCFpDCdMYNKhNVDvnKVJE64sCnqc=", "tF+PGpRhxrdkPIJIf4SAzqY72/MjlhQnaTc6hqC48Ps=", "mAMmpdLytq2qnplBJW4oMe8ekPpdzb5U04phoS02w/Y=", "Slfb1kmpR0poMLV7r4nyFjL4rHxoJYP1ZCq7+23Q22I=", "ksWV8RceooxeaDfwWIYaQWC5oVrS6tPP4/jxHG3MOg4=", "y5md3tv/UjcrY/9ZWxP1QfFSWF1qg1gbK7e7xon70mI=", "FnrN95I0zioklp5AQXCbp0YW4W90bHKQc0uRwa3KrSA=", "lt5/ICwFWIzEoPborJa8uCsryOCIZdLESs1IZKPQJ0Q=", "hJ27et6rItfuQ3YAelcOLEBUV5fk9kBVdpToKrKjlNk=", "sHb1/MmBVTJp2DXSgtwhI5scfAj6h6JLtlQX2vceZyI=", "TWCzSIVTYLCI+AXS9R/95Ky96ftjCa+uDmbY9cIIxUE=", "CH1WiPBA6diGQDdufL3RByo+Xcyt0qHqOylkCTCpTLw=", "WfZvy1sJ7YeR0eoMqgfpzQOB+B1vBA3CLCxBBjpmPqU=", "r/TtCFy5Fqh24/tSyoGLJwoTsvgotc8AuZQvdrIo44g=", "dhL4wpcEF8ZVhklMz2hJ9Im2xGN9c9WmC0+6jFuKCVY=", "FqZ1IS21gaPuUW7pYej2Hq1twIHee2/TFonKhji0Sug=", "6nITIvM/Glq27mcMeTHX9xN2iNoBL5MeHPH06I/v2AI=", "sIT2gF3jA9KQ9CVtaLSVQn2g9Rs26PbNaeiqw46hCts=", "er1buiH+VyTWdrbtrxU8jdY17TJYo/uNGEIKvuIytFk=", "fGJ4+UuNaqcg4DRUydHtVWG5VZydAjKslz815TDygn8=", "Ywb6XfdDb9hFP+d/ZJuF2kgN6bf2MLJCyNGo9DeH0FM=", "Wn0n2P5NrYvSVMo9L1gCHNv1rZkod0zEaoXLKj8OUxY=", "CDkTcAjN6Hr5sLX6MN4dFKpSrxYjbfIQDjQx9tsd/R4=", "O68IOFMiGMy8HESyc7ac1W01CFVSPVMmEwPPZYkuUik=", "CO/jQcb8Zwiw00mMRwXPJjWxRjnT+B434RVoEo2FkUA=", "7yBrTaj8cnSgNJC2F+hiY/K89xixJzsG/v6WwXWffQg=", "DMUH/s+BRNShLZiJMKPspp3fQzBjScTr1gA2jfL7i3M=", "Au1+qe1/8VZHuvhFGRrYsBVvToP6wZoTjg3mHi2fH7w=", "th1FuFuoUznK/zkQRldeLA/OMvxKivg7pPX02A/SDXo=", "hVai1GsXsnBlMSnbfz/xRL6v3LX9GhB6cWfpXxYY/Hk=", "JiJR1Udk+0JyPzdXOfok4S8yIPon0Sok4/AUzYzNFmY=", "vTbp9Pi0jTk/B0eL9H/NAEvMHt2tL5DLDxyakA5jI4Q=", "nGhIUWfIKW4Y28tlgq/raj3tb/92yfKAAQMDP6CZye4=", "BEuXtwsAiimtXHPZZeIumCTFffm8gIH8PhbW9rKadjw=", "zImV4LedQV76Hv/SPrSCCrn2lImkmLLluhheWtzLbD0=", "yrewNn+QDwWUbWe/Xgn7LCwNhjkF43q8+j+WIln1i9o=", "ETe3OHV1W495NZJbTBkQWkMvqGlU9T4SER/J51SwCXg=", "XwnKrTpuDAp0zSOoyGohwckdesv0AP3kMUvYLmKGSOY=", "CnfkjFLSxQkouEG9n+Sff1R5D4ipQSD2lN7ia7asA3I=", "Moi3i3xzc1Xm0ouGcINFt+tM/o4XEB6sjiYrIKdRwCs="], "CachedAssets": {"Au1+qe1/8VZHuvhFGRrYsBVvToP6wZoTjg3mHi2fH7w=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-09-05T15:11:16.3715945+00:00"}, "DMUH/s+BRNShLZiJMKPspp3fQzBjScTr1gA2jfL7i3M=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-09-05T15:11:16.368182+00:00"}, "7yBrTaj8cnSgNJC2F+hiY/K89xixJzsG/v6WwXWffQg=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-09-05T15:11:16.363148+00:00"}, "CO/jQcb8Zwiw00mMRwXPJjWxRjnT+B434RVoEo2FkUA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-09-05T15:11:16.3456246+00:00"}, "O68IOFMiGMy8HESyc7ac1W01CFVSPVMmEwPPZYkuUik=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-09-05T15:11:16.3305241+00:00"}, "CDkTcAjN6Hr5sLX6MN4dFKpSrxYjbfIQDjQx9tsd/R4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-09-05T15:11:16.3305241+00:00"}, "Wn0n2P5NrYvSVMo9L1gCHNv1rZkod0zEaoXLKj8OUxY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-09-05T15:11:16.3305241+00:00"}, "Ywb6XfdDb9hFP+d/ZJuF2kgN6bf2MLJCyNGo9DeH0FM=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-09-05T15:11:16.3236351+00:00"}, "fGJ4+UuNaqcg4DRUydHtVWG5VZydAjKslz815TDygn8=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-09-05T15:11:16.3179354+00:00"}, "er1buiH+VyTWdrbtrxU8jdY17TJYo/uNGEIKvuIytFk=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-09-05T15:11:16.3029302+00:00"}, "sIT2gF3jA9KQ9CVtaLSVQn2g9Rs26PbNaeiqw46hCts=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-09-05T15:11:16.3029302+00:00"}, "6nITIvM/Glq27mcMeTHX9xN2iNoBL5MeHPH06I/v2AI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-09-05T15:11:16.2848261+00:00"}, "FqZ1IS21gaPuUW7pYej2Hq1twIHee2/TFonKhji0Sug=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-09-05T15:11:16.2705785+00:00"}, "dhL4wpcEF8ZVhklMz2hJ9Im2xGN9c9WmC0+6jFuKCVY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-09-05T15:11:16.2492515+00:00"}, "r/TtCFy5Fqh24/tSyoGLJwoTsvgotc8AuZQvdrIo44g=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-09-05T15:11:16.2371433+00:00"}, "WfZvy1sJ7YeR0eoMqgfpzQOB+B1vBA3CLCxBBjpmPqU=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-09-05T15:11:16.2176019+00:00"}, "CH1WiPBA6diGQDdufL3RByo+Xcyt0qHqOylkCTCpTLw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-09-05T15:11:16.2018703+00:00"}, "TWCzSIVTYLCI+AXS9R/95Ky96ftjCa+uDmbY9cIIxUE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-09-05T15:11:16.1867726+00:00"}, "sHb1/MmBVTJp2DXSgtwhI5scfAj6h6JLtlQX2vceZyI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-09-05T15:11:16.1796545+00:00"}, "hJ27et6rItfuQ3YAelcOLEBUV5fk9kBVdpToKrKjlNk=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-09-05T15:11:16.1559877+00:00"}, "lt5/ICwFWIzEoPborJa8uCsryOCIZdLESs1IZKPQJ0Q=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-09-05T15:11:16.1541769+00:00"}, "FnrN95I0zioklp5AQXCbp0YW4W90bHKQc0uRwa3KrSA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-09-05T15:11:16.1505696+00:00"}, "y5md3tv/UjcrY/9ZWxP1QfFSWF1qg1gbK7e7xon70mI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-09-05T15:11:16.147097+00:00"}, "ksWV8RceooxeaDfwWIYaQWC5oVrS6tPP4/jxHG3MOg4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-09-05T15:11:16.1311622+00:00"}, "Slfb1kmpR0poMLV7r4nyFjL4rHxoJYP1ZCq7+23Q22I=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-09-05T15:11:16.1311622+00:00"}, "mAMmpdLytq2qnplBJW4oMe8ekPpdzb5U04phoS02w/Y=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-09-05T15:11:16.1227637+00:00"}, "tF+PGpRhxrdkPIJIf4SAzqY72/MjlhQnaTc6hqC48Ps=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-09-05T15:11:16.1141687+00:00"}, "AkX7Jq13/q1jK55nCFpDCdMYNKhNVDvnKVJE64sCnqc=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-09-05T15:11:16.1121563+00:00"}, "l4YSFEqCuQ5s4pzUDweMFLc1JRb+Gd4kvcFWUqimT+E=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-09-05T15:11:16.1081395+00:00"}, "VtYmQHBaivCnZTXnMMvWpokFqRqfJqAzV8NrTNQ+duY=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-09-05T15:11:16.0972475+00:00"}, "HyWjMXy70mMjYORF6hKtWISf2qzxbmtTB1xlRV2qmPs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-09-05T15:11:16.0972475+00:00"}, "o2euMkQnr6q09bCN1IknMtWZWk8c8bRu+TZQA6ot5e4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-09-05T15:11:16.0847584+00:00"}, "wJiA12qaAJZhXHubZ6Y11u5a96p5WMse9N6MvP7IuUI=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-09-05T15:11:16.0816819+00:00"}, "x39EPruyB2IcqOk5rcgJaVNlSuu+PJ/kDeBi7aj2tgw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-09-05T15:11:16.0816819+00:00"}, "fdkyQWn9q4CcaH+66AYdf08F5AyJxfDUlIhqScgO7uQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-09-05T15:11:16.0751437+00:00"}, "m4xKa0KWDV4cOvEPc4Y+sbj6NbJeOEi3M5urduHXNO4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-09-05T15:11:16.0716568+00:00"}, "sbqMFFJJvIZWN2co1UoSYTHaf9xW5e7YxmZV6FPloD4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-09-05T15:11:16.0716568+00:00"}, "AlIjdrut1iw/gbNzWHTNZktLNwPkP/KMUj3QmvB5goA=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-09-05T15:11:16.0594271+00:00"}, "4vFvrSZdtLXAeeiwwJ2BU+QbCIHf0aGZA8gxZ2zA8o4=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-09-05T15:11:16.0594271+00:00"}, "eYTfDswpJ8NRd+Kcb0Xf5j0y7UH1GlDiz5ySSyS4ghQ=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-09-05T15:11:16.0437018+00:00"}, "eFFyuKujv752qMxWAWgP5FuCsxYV4pB7tNtLuIbMJIs=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-09-05T15:11:16.0437018+00:00"}, "FpJLMwtwjmeC9nw4AyH7Bflqe3UrzE8fBR1uM6/HQoE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-09-05T15:11:16.0437018+00:00"}, "S8KXfOG8Md976OAWa+J+sSvypyvox1z6X9A61x8djKE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-09-05T15:11:16.0336343+00:00"}, "47A14t5es/JUSNotjqEJOYZ+bTxukI9QWYhnZIyXiDE=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-09-05T15:11:16.0336343+00:00"}, "S1SOZBtAt3QTFbZ6I86s1QKp/ToZgeltVIMlA+LY1kw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\favicon.png", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-09-05T15:11:15.9795199+00:00"}, "zmfMg+L9oV5b2ctSFTNbRaMRyfeHkyj6goXtNZvzSTw=": {"Identity": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\app.css", "SourceId": "OneHousingApiTester", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - Hedyn\\Documents\\augment-projects\\Onehousing API tester\\OneHousingApiTester\\wwwroot\\", "BasePath": "_content/OneHousingApiTester", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cukhpdzbwt", "Integrity": "xIBHbLOku2pGtC+byWmbCP3E2kT3Qc8nga4H63lsVqo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3498, "LastWriteTime": "2025-09-05T15:13:36.2272594+00:00"}}, "CachedCopyCandidates": {}}