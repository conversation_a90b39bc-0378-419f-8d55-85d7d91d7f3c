@page "/api-tester"
@using OneHousingApiTester.Services
@inject OneHousingApiService ApiService
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>OneHousing API Tester</PageTitle>

<h1>OneHousing API Tester</h1>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3>API Request Configuration</h3>
            </div>
            <div class="card-body">
                <EditForm Model="@apiRequest" OnValidSubmit="@SendRequest">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="url" class="form-label">API URL:</label>
                        <InputText id="url" class="form-control" @bind-Value="apiRequest.Url" />
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username:</label>
                        <InputText id="username" class="form-control" @bind-Value="apiRequest.Username" />
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password:</label>
                        <InputText id="password" type="password" class="form-control" @bind-Value="apiRequest.Password" />
                    </div>

                    <div class="mb-3">
                        <label for="requestType" class="form-label">Request Type:</label>
                        <InputText id="requestType" class="form-control" @bind-Value="apiRequest.RequestType" />
                    </div>

                    <div class="mb-3">
                        <label for="attributeName" class="form-label">Attribute Name:</label>
                        <InputText id="attributeName" class="form-control" @bind-Value="apiRequest.AttributeName" />
                    </div>

                    <div class="mb-3">
                        <label for="attributeValue" class="form-label">Attribute Value:</label>
                        <InputText id="attributeValue" class="form-control" @bind-Value="apiRequest.AttributeValue" />
                    </div>

                    <button type="submit" class="btn btn-primary" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <text> Sending...</text>
                        }
                        else
                        {
                            <text>Send Request</text>
                        }
                    </button>
                </EditForm>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3>Request XML Preview</h3>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>@GetPreviewXml()</code></pre>
            </div>
        </div>
    </div>
</div>

@if (apiResponse != null)
{
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>API Response</h3>
                    <span class="badge @(apiResponse.IsSuccess ? "bg-success" : "bg-danger")">
                        @apiResponse.StatusCode @apiResponse.StatusDescription
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Response received at: @apiResponse.ResponseTime.ToString("yyyy-MM-dd HH:mm:ss")</small>
                    </div>
                    
                    <ul class="nav nav-tabs" id="responseTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="formatted-tab" data-bs-toggle="tab" data-bs-target="#formatted" type="button" role="tab">
                                Formatted XML
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw" type="button" role="tab">
                                Raw Response
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="responseTabContent">
                        <div class="tab-pane fade show active" id="formatted" role="tabpanel">
                            <pre class="bg-light p-3 rounded mt-3" style="max-height: 400px; overflow-y: auto;"><code>@apiResponse.FormattedXml</code></pre>
                        </div>
                        <div class="tab-pane fade" id="raw" role="tabpanel">
                            <pre class="bg-light p-3 rounded mt-3" style="max-height: 400px; overflow-y: auto;"><code>@apiResponse.ResponseContent</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private ApiRequest apiRequest = new()
    {
        Url = "https://portaltest.melinhomes.co.uk/oa.test/webservice.p",
        Username = "SNAPONE",
        Password = "U33@8RchyyRA2Mg3",
        RequestType = "GETPLACE",
        AttributeName = "place_ref",
        AttributeValue = "8763"
    };

    private ApiResponse? apiResponse;
    private bool isLoading = false;

    private async Task SendRequest()
    {
        isLoading = true;
        apiResponse = null;
        StateHasChanged();

        try
        {
            apiResponse = await ApiService.SendRequestAsync(apiRequest);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetPreviewXml()
    {
        return $@"<message>
  <Header>
    <Security username='{apiRequest.Username}' password='{apiRequest.Password}' />
  </Header>
  <Body>
    <Request request_type='{apiRequest.RequestType}'>
      <Parameters attribute='{apiRequest.AttributeName}' attribute_value='{apiRequest.AttributeValue}' />
    </Request>
  </Body>
</message>";
    }
}
