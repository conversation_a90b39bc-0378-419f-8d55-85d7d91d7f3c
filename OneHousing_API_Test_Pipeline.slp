{"class_id": "com.snaplogic.pipeline.Pipeline", "class_version": 1, "class_map": {"com.snaplogic.pipeline.Pipeline": {"class_id": "com.snaplogic.pipeline.Pipeline", "class_version": 1, "instance_map": {"pipeline": {"class_id": "com.snaplogic.pipeline.PipelineDocument", "class_version": 1, "instance_map": {"snap_map": {"1": {"class_id": "com.snaplogic.snaps.flow.JSONGenerator", "class_version": 1, "instance_map": {"view_serial": 100, "settings": {"execution_mode": "Validate & Execute", "json_data": "{\n  \"api_url\": \"https://portaltest.melinhomes.co.uk/oa.test/webservice.p\",\n  \"username\": \"SNAPONE\",\n  \"password\": \"U33@8RchyyRA2Mg3\",\n  \"request_type\": \"GETPLACE\",\n  \"attribute_name\": \"place_ref\",\n  \"attribute_value\": \"8763\"\n}"}, "class_build_tag": "LATEST"}}, "2": {"class_id": "com.snaplogic.snaps.transform.Mapper", "class_version": 1, "instance_map": {"view_serial": 101, "settings": {"execution_mode": "Validate & Execute", "passThrough": false, "mapping": {"mapping_table": [{"source_path": "$api_url", "target_path": "$url"}, {"source_path": "$username", "target_path": "$auth_username"}, {"source_path": "$password", "target_path": "$auth_password"}, {"source_path": "$request_type", "target_path": "$req_type"}, {"source_path": "$attribute_name", "target_path": "$attr_name"}, {"source_path": "$attribute_value", "target_path": "$attr_value"}, {"expression": "\"<message><Header><Security username='\" + $auth_username + \"' password='\" + $auth_password + \"' /></Header><Body><Request request_type='\" + $req_type + \"'><Parameters attribute='\" + $attr_name + \"' attribute_value='\" + $attr_value + \"' /></Request></Body></message>\"", "target_path": "$xml_payload"}, {"expression": "Date.now()", "target_path": "$request_timestamp"}]}}, "class_build_tag": "LATEST"}}, "3": {"class_id": "com.snaplogic.snaps.http.HttpClient", "class_version": 1, "instance_map": {"view_serial": 102, "settings": {"execution_mode": "Validate & Execute", "url": "$url", "http_method": "POST", "request_headers": {"Content-Type": "application/xml", "Accept": "application/xml"}, "request_body": "$xml_payload", "timeout": 30, "follow_redirects": true, "trust_all_certs": false, "pagination": {"has_next": false}, "response_entity_type": "TEXT"}, "class_build_tag": "LATEST"}}, "4": {"class_id": "com.snaplogic.snaps.transform.Mapper", "class_version": 1, "instance_map": {"view_serial": 103, "settings": {"execution_mode": "Validate & Execute", "passThrough": false, "mapping": {"mapping_table": [{"source_path": "$entity", "target_path": "$response_body"}, {"source_path": "$response.status_code", "target_path": "$status_code"}, {"source_path": "$response.reason_phrase", "target_path": "$status_message"}, {"source_path": "$response.headers", "target_path": "$response_headers"}, {"expression": "Date.now()", "target_path": "$response_timestamp"}, {"expression": "$response_timestamp - $request_timestamp", "target_path": "$response_time_ms"}, {"expression": "$status_code >= 200 && $status_code < 300", "target_path": "$is_success"}, {"expression": "try { sl.parseXML($response_body); true } catch (e) { false }", "target_path": "$is_valid_xml"}, {"expression": "$is_valid_xml ? sl.formatXML($response_body) : $response_body", "target_path": "$formatted_response"}]}}, "class_build_tag": "LATEST"}}, "5": {"class_id": "com.snaplogic.snaps.transform.Mapper", "class_version": 1, "instance_map": {"view_serial": 104, "settings": {"execution_mode": "Validate & Execute", "passThrough": false, "mapping": {"mapping_table": [{"expression": "{\n  \"test_results\": {\n    \"timestamp\": new Date($response_timestamp).toISOString(),\n    \"request\": {\n      \"url\": $url,\n      \"method\": \"POST\",\n      \"xml_payload\": $xml_payload\n    },\n    \"response\": {\n      \"status_code\": $status_code,\n      \"status_message\": $status_message,\n      \"is_success\": $is_success,\n      \"response_time_ms\": $response_time_ms,\n      \"is_valid_xml\": $is_valid_xml,\n      \"headers\": $response_headers,\n      \"body\": $response_body,\n      \"formatted_xml\": $formatted_response\n    },\n    \"summary\": {\n      \"test_passed\": $is_success && $is_valid_xml,\n      \"error_message\": $is_success ? null : \"HTTP \" + $status_code + \": \" + $status_message\n    }\n  }\n}", "target_path": "$"}]}}, "class_build_tag": "LATEST"}}, "6": {"class_id": "com.snaplogic.snaps.flow.JSONFormatter", "class_version": 1, "instance_map": {"view_serial": 105, "settings": {"execution_mode": "Validate & Execute", "pretty_print": true, "ascii_encode_strings": false}, "class_build_tag": "LATEST"}}}, "link_map": {"1": [{"src_id": "1", "src_view_id": "output0", "dst_id": "2", "dst_view_id": "input0"}], "2": [{"src_id": "2", "src_view_id": "output0", "dst_id": "3", "dst_view_id": "input0"}], "3": [{"src_id": "3", "src_view_id": "output0", "dst_id": "4", "dst_view_id": "input0"}], "4": [{"src_id": "4", "src_view_id": "output0", "dst_id": "5", "dst_view_id": "input0"}], "5": [{"src_id": "5", "src_view_id": "output0", "dst_id": "6", "dst_view_id": "input0"}]}, "property_map": {"error": {"error_behavior": {"fail_on_error": true, "failure_message": "OneHousing API Test Pipeline failed", "failure_message_expression": false}}, "info": {"author": "API Tester", "purpose": "Test OneHousing API endpoints with XML payloads", "label": "OneHousing API Test Pipeline", "notes": "This pipeline tests the OneHousing API by sending XML requests and validating responses. Configure the JSON Generator with your specific test parameters."}, "input": {}, "output": {}, "settings": {"cache_pipelines": false, "error_param_table": {}, "param_table": {}, "suspendable": false, "test_pipeline": false}}, "render_map": {"default_snaplex": "snaplex1", "detail_map": {"1": {"grid_x_int": 1, "grid_y_int": 1, "index": 1, "label": "API Parameters", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "2": {"grid_x_int": 2, "grid_y_int": 1, "index": 2, "label": "Build XML Request", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "3": {"grid_x_int": 3, "grid_y_int": 1, "index": 3, "label": "HTTP API Call", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "4": {"grid_x_int": 4, "grid_y_int": 1, "index": 4, "label": "Process Response", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "5": {"grid_x_int": 5, "grid_y_int": 1, "index": 5, "label": "Format Results", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "6": {"grid_x_int": 6, "grid_y_int": 1, "index": 6, "label": "JSON Output", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}}}}}}}}}