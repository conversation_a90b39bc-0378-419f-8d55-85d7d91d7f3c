using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace OneHousingApiTester.Services;

public class OneHousingApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OneHousingApiService> _logger;

    public OneHousingApiService(HttpClient httpClient, ILogger<OneHousingApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<ApiResponse> SendRequestAsync(ApiRequest request)
    {
        var startTime = DateTime.Now;
        try
        {
            var xmlPayload = BuildXmlPayload(request);
            _logger.LogInformation("Sending request to: {Url}", request.Url);
            _logger.LogInformation("XML Payload: {Payload}", xmlPayload);

            var content = new StringContent(xmlPayload, Encoding.UTF8, "application/xml");

            var response = await _httpClient.PostAsync(request.Url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            var endTime = DateTime.Now;

            var formattedXml = FormatXml(responseContent);
            var isValidXml = IsValidXml(responseContent);

            return new ApiResponse
            {
                IsSuccess = response.IsSuccessStatusCode,
                StatusCode = (int)response.StatusCode,
                StatusDescription = response.ReasonPhrase ?? string.Empty,
                ResponseContent = responseContent,
                FormattedXml = formattedXml,
                RequestXml = xmlPayload,
                ResponseTime = endTime,
                IsValidXml = isValidXml,
                Duration = endTime - startTime,
                ErrorMessage = response.IsSuccessStatusCode ? string.Empty : $"HTTP {response.StatusCode}: {response.ReasonPhrase}"
            };
        }
        catch (Exception ex)
        {
            var endTime = DateTime.Now;
            _logger.LogError(ex, "Error sending API request");
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = 0,
                StatusDescription = "Exception occurred",
                ResponseContent = ex.Message,
                FormattedXml = string.Empty,
                RequestXml = string.Empty,
                ResponseTime = endTime,
                IsValidXml = false,
                Duration = endTime - startTime,
                ErrorMessage = ex.Message
            };
        }
    }

    private string BuildXmlPayload(ApiRequest request)
    {
        var message = new XElement("message",
            new XElement("Header",
                new XElement("Security",
                    new XAttribute("username", request.Username),
                    new XAttribute("password", request.Password)
                )
            ),
            new XElement("Body",
                new XElement("Request",
                    new XAttribute("request_type", request.RequestType),
                    new XElement("Parameters",
                        new XAttribute("attribute", request.AttributeName),
                        new XAttribute("attribute_value", request.AttributeValue)
                    )
                )
            )
        );

        return message.ToString();
    }

    private string FormatXml(string xml)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(xml))
                return string.Empty;

            // Try to parse as XML and format it
            var doc = XDocument.Parse(xml);
            return doc.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Failed to format XML: {Error}", ex.Message);
            // If it's not valid XML, return the original content
            return xml;
        }
    }

    public bool IsValidXml(string xml)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(xml))
                return false;

            XDocument.Parse(xml);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

public class ApiRequest
{
    public string Url { get; set; } = string.Empty;
    public string Username { get; set; } = "SNAPONE";
    public string Password { get; set; } = "U33@8RchyyRA2Mg3";
    public string RequestType { get; set; } = "GETPLACE";
    public string AttributeName { get; set; } = "place_ref";
    public string AttributeValue { get; set; } = "8763";
}

public class ApiResponse
{
    public bool IsSuccess { get; set; }
    public int StatusCode { get; set; }
    public string StatusDescription { get; set; } = string.Empty;
    public string ResponseContent { get; set; } = string.Empty;
    public string FormattedXml { get; set; } = string.Empty;
    public string RequestXml { get; set; } = string.Empty;
    public DateTime ResponseTime { get; set; }
    public bool IsValidXml { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public TimeSpan? Duration { get; set; }
}
