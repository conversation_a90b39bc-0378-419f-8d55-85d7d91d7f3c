# OneHousing API Tester

A C# Blazor web application for testing OneHousing API endpoints with XML payloads.

## Features

- **Interactive Web Interface**: Easy-to-use form for configuring API requests
- **XML Request Builder**: Automatically generates properly formatted XML payloads
- **Real-time Preview**: See the XML request before sending
- **Response Viewer**: View both formatted XML and raw response data
- **Error Handling**: Clear error messages and HTTP status codes
- **Response Timing**: Track request duration and response times

## Getting Started

### Prerequisites

- .NET 9.0 SDK or later
- A web browser

### Running the Application

1. Clone or download this repository
2. Navigate to the `OneHousingApiTester` directory
3. Run the application:
   ```bash
   dotnet run
   ```
4. Open your browser and navigate to `http://localhost:5260`

### Using the API Tester

1. Click on **"API Tester"** in the navigation menu
2. Configure your API request:
   - **API URL**: The endpoint URL (default: OneHousing test environment)
   - **Username**: API username (default: SNAPONE)
   - **Password**: API password (pre-configured)
   - **Request Type**: Type of request (default: GETPLACE)
   - **Attribute Name**: Parameter name (default: place_ref)
   - **Attribute Value**: Parameter value (default: 8763)

3. Review the XML preview on the right side
4. Click **"Send Request"** to execute the API call
5. View the response in the formatted XML or raw response tabs

## Default Configuration

The application comes pre-configured for the OneHousing test environment:

- **URL**: `https://portaltest.melinhomes.co.uk/oa.test/webservice.p`
- **Username**: `SNAPONE`
- **Password**: `U33@8RchyyRA2Mg3`
- **Request Type**: `GETPLACE`
- **Sample Property Reference**: `8763`

## Sample XML Request

```xml
<message>
  <Header>
    <Security username='SNAPONE' password='U33@8RchyyRA2Mg3' />
  </Header>
  <Body>
    <Request request_type='GETPLACE'>
      <Parameters attribute='place_ref' attribute_value='8763' />
    </Request>
  </Body>
</message>
```

## Project Structure

```
OneHousingApiTester/
├── Components/
│   ├── Layout/          # Navigation and layout components
│   └── Pages/           # Blazor pages
│       ├── Home.razor   # Welcome page
│       └── ApiTester.razor  # Main API testing interface
├── Services/
│   └── OneHousingApiService.cs  # API service for HTTP requests
├── wwwroot/             # Static files and CSS
└── Program.cs           # Application startup

OneHousingApiTester.Tests/
└── ApiServiceTests.cs   # Unit tests for the API service
```

## Testing

Run the unit tests:

```bash
dotnet test OneHousingApiTester.Tests
```

## Development

### Adding New Request Types

To add support for new request types:

1. Update the `ApiRequest` class in `Services/OneHousingApiService.cs`
2. Modify the `BuildXmlPayload` method to handle new request structures
3. Update the UI in `Components/Pages/ApiTester.razor` as needed

### Customizing the UI

- Modify `Components/Pages/ApiTester.razor` for UI changes
- Update `wwwroot/app.css` for styling changes
- Edit `Components/Layout/NavMenu.razor` for navigation changes

## Technologies Used

- **ASP.NET Core 9.0**
- **Blazor Server**
- **Bootstrap 5** (for styling)
- **System.Xml.Linq** (for XML handling)
- **xUnit** (for testing)
- **Moq** (for mocking in tests)

## License

This project is for internal use and testing of OneHousing API endpoints.
