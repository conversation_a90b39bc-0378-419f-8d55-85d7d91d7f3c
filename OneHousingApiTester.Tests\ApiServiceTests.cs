using Microsoft.Extensions.Logging;
using Moq;
using OneHousingApiTester.Services;
using System.Net;
using System.Text;
using Xunit;

namespace OneHousingApiTester.Tests;

public class ApiServiceTests
{
    [Fact]
    public void IsValidXml_WithValidXml_ReturnsTrue()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<OneHousingApiService>>();
        var mockHttpClient = new Mock<HttpClient>();
        var service = new OneHousingApiService(mockHttpClient.Object, mockLogger.Object);
        
        var validXml = @"<message>
            <Header>
                <Security username='SNAPONE' password='U33@8RchyyRA2Mg3' />
            </Header>
            <Body>
                <Request request_type='GETPLACE'>
                    <Parameters attribute='place_ref' attribute_value='8763' />
                </Request>
            </Body>
        </message>";

        // Act
        var result = service.IsValidXml(validXml);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsValidXml_WithInvalidXml_ReturnsFalse()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<OneHousingApiService>>();
        var mockHttpClient = new Mock<HttpClient>();
        var service = new OneHousingApiService(mockHttpClient.Object, mockLogger.Object);
        
        var invalidXml = "<message><unclosed>";

        // Act
        var result = service.IsValidXml(invalidXml);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsValidXml_WithEmptyString_ReturnsFalse()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<OneHousingApiService>>();
        var mockHttpClient = new Mock<HttpClient>();
        var service = new OneHousingApiService(mockHttpClient.Object, mockLogger.Object);

        // Act
        var result = service.IsValidXml(string.Empty);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void ApiRequest_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var request = new ApiRequest();

        // Assert
        Assert.Equal("SNAPONE", request.Username);
        Assert.Equal("U33@8RchyyRA2Mg3", request.Password);
        Assert.Equal("GETPLACE", request.RequestType);
        Assert.Equal("place_ref", request.AttributeName);
        Assert.Equal("8763", request.AttributeValue);
    }
}
