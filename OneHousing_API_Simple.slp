{"class_id": "com.snaplogic.pipeline.Pipeline", "class_version": 1, "class_map": {"com.snaplogic.pipeline.Pipeline": {"class_id": "com.snaplogic.pipeline.Pipeline", "class_version": 1, "instance_map": {"pipeline": {"class_id": "com.snaplogic.pipeline.PipelineDocument", "class_version": 1, "instance_map": {"snap_map": {"11111111-1111-1111-1111-111111111111": {"class_id": "com.snaplogic.snaps.flow.JSONGenerator", "class_version": 1, "instance_map": {"view_serial": 100, "settings": {"execution_mode": "Validate & Execute", "json_data": "{\n  \"url\": \"https://portaltest.melinhomes.co.uk/oa.test/webservice.p\",\n  \"xml_body\": \"<message><Header><Security username='SNAPONE' password='U33@8RchyyRA2Mg3' /></Header><Body><Request request_type='GETPLACE'><Parameters attribute='place_ref' attribute_value='8763' /></Request></Body></message>\"\n}"}, "class_build_tag": "LATEST"}}, "22222222-2222-2222-2222-222222222222": {"class_id": "com.snaplogic.snaps.http.HttpClient", "class_version": 1, "instance_map": {"view_serial": 101, "settings": {"execution_mode": "Validate & Execute", "url": "$url", "http_method": "POST", "request_headers": {"Content-Type": "application/xml", "Accept": "application/xml"}, "request_body": "$xml_body", "timeout": 30, "follow_redirects": true, "trust_all_certs": false, "response_entity_type": "TEXT"}, "class_build_tag": "LATEST"}}}, "link_map": {"11111111-1111-1111-1111-111111111111": [{"src_id": "11111111-1111-1111-1111-111111111111", "src_view_id": "output0", "dst_id": "22222222-2222-2222-2222-222222222222", "dst_view_id": "input0"}]}, "property_map": {"error": {"error_behavior": {"fail_on_error": true, "failure_message": "OneHousing API Test failed"}}, "info": {"author": "API Tester", "purpose": "Test OneHousing API with XML request", "label": "OneHousing API Test", "notes": "Simple pipeline to test OneHousing API endpoint"}, "input": {}, "output": {}, "settings": {"cache_pipelines": false, "error_param_table": {}, "param_table": {}, "suspendable": false, "test_pipeline": false}}, "render_map": {"default_snaplex": "snaplex1", "detail_map": {"11111111-1111-1111-1111-111111111111": {"grid_x_int": 1, "grid_y_int": 1, "index": 1, "label": "API Data", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}, "22222222-2222-2222-2222-222222222222": {"grid_x_int": 2, "grid_y_int": 1, "index": 2, "label": "HTTP Call", "rot_int": 0, "rot_tail_int": 0, "source": "snap catagory"}}}}}}}}}